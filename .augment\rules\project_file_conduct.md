---
type: "always_apply"
description: "Example description"
---

# 通用项目文件管理规则

## 核心原则

1. **保持根目录简洁**

   - 根目录应该只包含主要的功能模块文件夹
   - 避免在根目录下创建过多的临时文件夹或散乱文件
   - 所有相关的子项目应该归类到对应的主功能文件夹中
   - 根目录文件夹数量控制在10个以内（不含系统文件）

2. **按功能模块分类**（以下为举例）

   - **源码开发**: 统一放在 `src/` 或项目特定目录（如 `algorithms/`）
   - **逆向工程**: 统一放在 `reverse_engineering/` 目录下
   - **测试相关**: 放在对应项目的 `test/` 或 `test_programs/` 子目录下
   - **文档资料**: 放在 `docs/` 或 `reference/` 目录下
   - **配置文件**: 放在 `.config/` 或 `.augment/` 等隐藏目录下
   - **构建产物**: 放在 `build/`、`dist/`、`executables/` 等目录下




### 3. 版本管理规则

- 同一项目的不同版本使用 `_v1`, `_v2`, `_v3` 等后缀
- 每个版本的源文件开头必须包含详细的改动说明注释
- 保留所有版本的源代码，但只部署最新稳定版本到engines目录

### 4. 文件命名规则

- 使用英文命名，避免中文文件名
- 使用下划线分隔单词：`project_name_v1.cpp`
- 测试文件以 `test_` 开头：`test_project_name.py`
- 结果文件以时间戳结尾：`battle_result_20250726.json`

### 5. 临时文件处理

- 编译产生的中间文件（.o, .obj等）应及时清理
- 测试产生的日志文件应放在对应的results子目录
- 不要在根目录留下临时文件

### 6. 文档管理

- 每个项目必须有README.md说明文档
- 重要的分析报告放在对应项目的analysis_results目录
- 全局的规则文档放在 `.augment/rules/` 目录

### 7. 测试程序规则

- 不要重复创建对战系统，使用现有的battle_system
- 测试程序应该调用现有的测试框架，而不是重新实现
- 简单的功能测试可以放在项目的test_programs目录

### 8. 日志和缓存管理

- 编译产生的中间文件（.o, .obj, .pyc等）应及时清理
- 运行时日志统一放在logs目录
- 缓存文件夹（__pycache__等）应添加到.gitignore

## 通用执行步骤

### 创建新项目时：

1. 确定项目类型（逆向工程/算法开发/Web开发/数据分析等）
2. 在对应的主目录下创建项目文件夹
3. 按照标准结构创建子目录
4. 创建README.md说明文档
5. 设置适当的.gitignore文件

### 文件整理时：

1. 检查根目录是否有临时文件夹或散乱文件
2. 将相关文件移动到正确的分类目录
3. 删除空的临时文件夹
4. 清理编译产生的中间文件和缓存
5. 整理日志文件到logs目录

### 版本更新时：

1. 创建新版本文件（添加版本后缀）
2. 在文件开头添加详细的改动说明
3. 更新README.md文档
4. 测试新版本功能
5. 部署稳定版本到相应的部署目录

## 示例目录结构

### 游戏AI开发项目

````
SAU_Game_Platform_2.1.0_r4/
├── SmartDotsAndBoxes_Engine/          # 主要引擎目录
│   ├── algorithms/                    # 算法开发
│   ├── engines/                       # 部署的引擎
│   ├── battle_system/                # 对战系统（现有）
│   └── results/                      # 结果文件
├── reverse_engineering/               # 逆向工程
│   ├── IDA_MCP_Complete_Reconstruction/
│   ├── Ghidra_Analysis/
│   └── ...
├── reference/                         # 参考资料
├── .augment/                         # 配置和规则
│   └── rules/
└── README.md                         # 项目总说明
````

### Web开发项目

````
WebProject/
├── frontend/                          # 前端代码
│   ├── src/                          # 源代码
│   ├── public/                       # 静态资源
│   ├── build/                        # 构建产物
│   └── test/                         # 测试文件
├── backend/                          # 后端代码
│   ├── src/                          # 源代码
│   ├── api/                          # API接口
│   ├── models/                       # 数据模型
│   └── test/                         # 测试文件
├── docs/                             # 文档
├── logs/                             # 日志
├── .config/                          # 配置文件
└── README.md
````

### 数据分析项目

````
DataAnalysisProject/
├── data/                             # 数据文件
│   ├── raw/                          # 原始数据
│   ├── processed/                    # 处理后数据
│   └── external/                     # 外部数据
├── notebooks/                        # Jupyter笔记本
├── src/                              # 源代码
│   ├── data_processing/              # 数据处理
│   ├── models/                       # 模型
│   └── visualization/                # 可视化
├── results/                          # 结果输出
├── docs/                             # 文档
└── README.md
````

## 注意事项

1. **始终保持根目录整洁**
2. **使用现有的测试系统，不要重复创建**
3. **及时清理临时文件和中间文件**
4. **每个项目都要有清晰的文档说明**
5. **版本管理要规范，保留历史版本**



