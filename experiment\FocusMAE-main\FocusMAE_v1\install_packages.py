import subprocess
import sys

# List of packages to install
packages = [
    "_libgcc_mutex==0.1",
    "_openmp_mutex==5.1",
    "absl-py==1.4.0",
    "addict==2.4.0",
    "albumentations==1.3.1",
    "arrow==1.3.0",
    "asttokens==2.2.1",
    "attrs==23.2.0",
    "av==10.0.0",
    "backcall==0.2.0",
    "backports==1.0",
    "backports.functools_lru_cache==1.6.5",
    "blas==1.0",
    "boto3==1.34.17",
    "botocore==1.34.17",
    "bottleneck==1.3.5",
    "bravado==11.0.3",
    "bravado-core==6.1.1",
    "brotlipy==0.7.0",
    "bzip2==1.0.8",
    "ca-certificates==2023.7.22",
    "cached-property==1.5.2",
    "cachetools==5.3.1",
    "certifi==2023.7.22",
    "cffi==1.15.1",
    "charset-normalizer==2.0.4",
    "chex==0.1.7",
    "click==8.1.7",
    "cmake==3.27.2",
    "comm==0.1.4",
    "contourpy==1.1.0",
    "cryptography==41.0.2",
    "cudatoolkit==11.3.1",
    "cycler==0.11.0",
    "debugpy==1.6.7",
    "decorator==5.1.1",
    "decord==0.6.0",
    "deepspeed==0.10.1",
    "dill==0.3.7",
    "dm-haiku==0.0.12.dev0",
    "dm-tree==0.1.8",
    "efficientnet-pytorch==0.7.1",
    "einops==0.6.1",
    "entrypoints==0.4",
    "etils==1.3.0",
    "executing==1.2.0",
    "ffmpeg==4.3",
    "filelock==3.12.2",
    "flax==0.7.2",
    "fonttools==4.42.1",
    "fqdn==1.5.1",
    "freetype==2.12.1",
    "fsspec==2023.6.0",
    "future==0.18.3",
    "giflib==5.2.1",
    "gitdb==4.0.11",
    "gitpython==3.1.41",
    "gmp==6.2.1",
    "gnutls==3.6.15",
    "google-auth==2.22.0",
    "google-auth-oauthlib==0.4.6",
    "grpcio==1.57.0",
    "hjson==3.1.0",
    "huggingface-hub==0.16.4",
    "idna==3.4",
    "imageio==2.31.5",
    "imageio-ffmpeg==0.4.9",
    "imgaug==0.4.0",
    "importlib-metadata==6.8.0",
    "importlib-resources==6.0.1",
    "intel-openmpz==2023.1.0",
    "ipykernel==6.25.1",
    "ipython==8.12.0",
    "isoduration==20.11.0",
    "jax==0.4.13",
    "jaxlib==0.4.13",
    "jedi==0.19.0",
    "jmespath==1.0.1",
    "jmp==0.0.4",
    "joblib==1.3.2",
    "jpeg==9e",
    "jsonpointer==2.4",
    "jsonref==1.1.0",
    "jsonschema==4.20.0",
    "jsonschema-specifications==2023.12.1",
    "jupyter_client==7.3.4",
    "jupyter_core==4.12.0",
    "kiwisolver==1.4.4",
    "lame==3.100",
    "lazy-loader==0.3",
    "lcms2==2.12",
    "ld_impl_linux-64==2.38",
    "lerc==3.0",
    "libdeflate==1.17",
    "libffi==3.4.4",
    "libgcc-ng==11.2.0",
    "libgfortran-ng==7.5.0",
    "libgfortran4==7.5.0",
    "libgomp==11.2.0",
    "libiconv==1.16",
    "libidn2==2.3.4",
    "libpng==1.6.39",
    "libsodium==1.0.18",
    "libstdcxx-ng==11.2.0",
    "libtasn1==4.19.0",
    "libtiff==4.5.0",
    "libunistring==0.9.10",
    "libwebp==1.2.4",
    "libwebp-base==1.2.4",
    "lz4-c==1.9.4",
    "markdown==3.4.4",
    "markdown-it-py==3.0.0",
    "markupsafe==2.1.3",
    "matplotlib==3.7.4",
    "matplotlib-inline==0.1.6",
    "mdurl==0.1.2",
    "mkl==2023.1.0",
    "mkl-service==2.4.0",
    "mkl_fft==1.3.6",
    "mkl_random==1.2.2",
    "ml-dtypes==0.2.0",
    "monotonic==1.6",
    "mpi==1.0",
    "mpi4py==3.1.4",
    "mpich==3.3.2",
    "msgpack==1.0.7",
    "multiscaledeformableattention==1.0",
    "munch==4.0.0",
    "ncurses==6.4",
    "neptune==1.8.6",
    "nest-asyncio==1.5.6",
    "nettle==3.7.3",
    "networkx==3.1",
    "ninja==1.11.1",
    "numexpr==2.8.4",
    "numpy==1.24.3",
    "numpy-base==1.24.3",
    "oauthlib==3.2.2",
    "opencv-python==********"
]

# Function to install packages
def install_packages(packages):
    for package in packages:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])

# Run the installation
if __name__ == "__main__":
    install_packages(packages)
