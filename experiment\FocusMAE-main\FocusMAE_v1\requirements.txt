# This file may be used to create an environment using:
# $ conda create --name <env> --file <this file>
# platform: linux-64
_libgcc_mutex=0.1=main
_openmp_mutex=5.1=1_gnu
absl-py=1.4.0=pypi_0
addict=2.4.0=pypi_0
albumentations=1.3.1=pypi_0
arrow=1.3.0=pypi_0
asttokens=2.2.1=pyhd8ed1ab_0
attrs=23.2.0=pypi_0
av=10.0.0=pypi_0
backcall=0.2.0=pyh9f0ad1d_0
backports=1.0=pyhd8ed1ab_3
backports.functools_lru_cache=1.6.5=pyhd8ed1ab_0
blas=1.0=mkl
boto3=1.34.17=pypi_0
botocore=1.34.17=pypi_0
bottleneck=1.3.5=py38h7deecbd_0
bravado=11.0.3=pypi_0
bravado-core=6.1.1=pypi_0
brotlipy=0.7.0=py38h27cfd23_1003
bzip2=1.0.8=h7b6447c_0
ca-certificates=2023.7.22=hbcca054_0
cached-property=1.5.2=pypi_0
cachetools=5.3.1=pypi_0
certifi=2023.7.22=pyhd8ed1ab_0
cffi=1.15.1=py38h5eee18b_3
charset-normalizer=2.0.4=pyhd3eb1b0_0
chex=0.1.7=pypi_0
click=8.1.7=pypi_0
cmake=3.27.2=pypi_0
comm=0.1.4=pyhd8ed1ab_0
contourpy=1.1.0=pypi_0
cryptography=41.0.2=py38h22a60cf_0
cudatoolkit=11.3.1=h2bc3f7f_2
cycler=0.11.0=pypi_0
debugpy=1.6.7=py38h6a678d5_0
decorator=5.1.1=pyhd8ed1ab_0
decord=0.6.0=pypi_0
deepspeed=0.10.1=pypi_0
dill=0.3.7=pypi_0
dm-haiku=0.0.12.dev0=pypi_0
dm-tree=0.1.8=pypi_0
efficientnet-pytorch=0.7.1=pypi_0
einops=0.6.1=pypi_0
entrypoints=0.4=pyhd8ed1ab_0
etils=1.3.0=pypi_0
executing=1.2.0=pyhd8ed1ab_0
ffmpeg=4.3=hf484d3e_0
filelock=3.12.2=pypi_0
flax=0.7.2=pypi_0
fonttools=4.42.1=pypi_0
fqdn=1.5.1=pypi_0
freetype=2.12.1=h4a9f257_0
fsspec=2023.6.0=pypi_0
future=0.18.3=pypi_0
giflib=5.2.1=h5eee18b_3
gitdb=4.0.11=pypi_0
gitpython=3.1.41=pypi_0
gmp=6.2.1=h295c915_3
gnutls=3.6.15=he1e5248_0
google-auth=2.22.0=pypi_0
google-auth-oauthlib=0.4.6=pypi_0
grpcio=1.57.0=pypi_0
hjson=3.1.0=pypi_0
huggingface-hub=0.16.4=pypi_0
idna=3.4=py38h06a4308_0
imageio=2.31.5=pypi_0
imageio-ffmpeg=0.4.9=pypi_0
imgaug=0.4.0=pypi_0
importlib-metadata=6.8.0=pypi_0
importlib-resources=6.0.1=pypi_0
intel-openmpz=2023.1.0=hdb19cb5_46305
ipykernel=6.25.1=pyh71e2992_0
ipython=8.12.0=pyh41d4057_0
isoduration=20.11.0=pypi_0
jax=0.4.13=pypi_0
jaxlib=0.4.13=pypi_0
jedi=0.19.0=pyhd8ed1ab_0
jmespath=1.0.1=pypi_0
jmp=0.0.4=pypi_0
joblib=1.3.2=pypi_0
jpeg=9e=h5eee18b_1
jsonpointer=2.4=pypi_0
jsonref=1.1.0=pypi_0
jsonschema=4.20.0=pypi_0
jsonschema-specifications=2023.12.1=pypi_0
jupyter_client=7.3.4=pyhd8ed1ab_0
jupyter_core=4.12.0=py38h578d9bd_0
kiwisolver=1.4.4=pypi_0
lame=3.100=h7b6447c_0
lazy-loader=0.3=pypi_0
lcms2=2.12=h3be6417_0
ld_impl_linux-64=2.38=h1181459_1
lerc=3.0=h295c915_0
libdeflate=1.17=h5eee18b_0
libffi=3.4.4=h6a678d5_0
libgcc-ng=11.2.0=h1234567_1
libgfortran-ng=7.5.0=ha8ba4b0_17
libgfortran4=7.5.0=ha8ba4b0_17
libgomp=11.2.0=h1234567_1
libiconv=1.16=h7f8727e_2
libidn2=2.3.4=h5eee18b_0
libpng=1.6.39=h5eee18b_0
libsodium=1.0.18=h36c2ea0_1
libstdcxx-ng=11.2.0=h1234567_1
libtasn1=4.19.0=h5eee18b_0
libtiff=4.5.0=h6a678d5_2
libunistring=0.9.10=h27cfd23_0
libwebp=1.2.4=h11a3e52_1
libwebp-base=1.2.4=h5eee18b_1
lz4-c=1.9.4=h6a678d5_0
markdown=3.4.4=pypi_0
markdown-it-py=3.0.0=pypi_0
markupsafe=2.1.3=pypi_0
matplotlib=3.7.4=pypi_0
matplotlib-inline=0.1.6=pyhd8ed1ab_0
mdurl=0.1.2=pypi_0
mkl=2023.1.0=h213fc3f_46343
mkl-service=2.4.0=py38h5eee18b_1
mkl_fft=1.3.6=py38h417a72b_1
mkl_random=1.2.2=py38h417a72b_1
ml-dtypes=0.2.0=pypi_0
monotonic=1.6=pypi_0
mpi=1.0=mpich
mpi4py=3.1.4=py38hfc96bbd_0
mpich=3.3.2=hc856adb_0
msgpack=1.0.7=pypi_0
multiscaledeformableattention=1.0=pypi_0
munch=4.0.0=pypi_0
ncurses=6.4=h6a678d5_0
neptune=1.8.6=pypi_0
nest-asyncio=1.5.6=pyhd8ed1ab_0
nettle=3.7.3=hbbd107a_1
networkx=3.1=pypi_0
ninja=1.11.1=pypi_0
numexpr=2.8.4=py38hc78ab66_1
numpy=1.24.3=py38hf6e8229_1
numpy-base=1.24.3=py38h060ed82_1
oauthlib=3.2.2=pypi_0
opencv-python=********=pypi_0
opencv-python-headless=********=pypi_0
openh264=2.1.1=h4ff587b_0
openssl=3.0.10=h7f8727e_2
opt-einsum=3.3.0=pypi_0
optax=0.1.8=pypi_0
orbax-checkpoint=0.2.3=pypi_0
packaging=23.1=pyhd8ed1ab_0
pandas=1.5.2=py38h417a72b_0
parso=0.8.3=pyhd8ed1ab_0
pexpect=4.8.0=pyh1a96a4e_2
pickleshare=0.7.5=py_1003
pillow=9.4.0=py38h6a678d5_0
pip=23.2.1=py38h06a4308_0
piqa=1.3.2=pypi_0
pkgutil-resolve-name=1.3.10=pypi_0
platformdirs=4.2.0=pypi_0
pretrainedmodels=0.7.4=pypi_0
prettytable=3.9.0=pypi_0
prompt-toolkit=3.0.39=pyha770c72_0
prompt_toolkit=3.0.39=hd8ed1ab_0
protobuf=4.24.1=pypi_0
psutil=5.9.5=pypi_0
ptyprocess=0.7.0=pyhd3deb0d_0
pure_eval=0.2.2=pyhd8ed1ab_0
py-cpuinfo=9.0.0=pypi_0
pyasn1=0.5.0=pypi_0
pyasn1-modules=0.3.0=pypi_0
pycocotools=2.0.7=pypi_0
pycparser=2.21=pyhd3eb1b0_0
pydantic=1.10.12=pypi_0
pydicom=2.4.4=pypi_0
pygments=2.16.1=pyhd8ed1ab_0
pyjwt=2.8.0=pypi_0
pyopenssl=23.2.0=py38h06a4308_0
pyparsing=3.0.9=pypi_0
pysocks=1.7.1=py38h06a4308_0
python=3.8.17=h955ad1f_0
python-dateutil=2.8.2=pyhd3eb1b0_0
python_abi=3.8=2_cp38
pytorch=1.12.1=py3.8_cuda11.3_cudnn8.3.2_0
pytorch-mutex=1.0=cuda
pytz=2022.7=py38h06a4308_0
pywavelets=1.4.1=pypi_0
pyyaml=6.0.1=pypi_0
pyzmq=25.1.0=py38h6a678d5_0
qudida=0.0.4=pypi_0
readline=8.2=h5eee18b_0
referencing=0.32.1=pypi_0
regex=2023.10.3=pypi_0
requests=2.31.0=py38h06a4308_0
requests-oauthlib=1.3.1=pypi_0
rfc3339-validator=0.1.4=pypi_0
rfc3986-validator=0.1.1=pypi_0
rich=13.7.0=pypi_0
rpds-py=0.16.2=pypi_0
rsa=4.9=pypi_0
s3transfer=0.10.0=pypi_0
safetensors=0.3.2=pypi_0
scikit-image=0.21.0=pypi_0
scikit-learn=1.3.2=pypi_0
scipy=1.10.1=pypi_0
setuptools=68.0.0=py38h06a4308_0
shapely=2.0.2=pypi_0
simplejson=3.19.2=pypi_0
six=1.16.0=pyhd3eb1b0_1
smmap=5.0.1=pypi_0
sqlite=3.41.2=h5eee18b_0
stack_data=0.6.2=pyhd8ed1ab_0
swagger-spec-validator=3.0.3=pypi_0
tabulate=0.9.0=pypi_0
tbb=2021.8.0=hdb19cb5_0
tensorboard=2.9.0=pypi_0
tensorboard-data-server=0.6.1=pypi_0
tensorboard-plugin-wit=1.8.1=pypi_0
tensorboardx=1.8=pypi_0
tensorstore=0.1.45=pypi_0
termcolor=2.4.0=pypi_0
threadpoolctl=3.2.0=pypi_0
tifffile=2023.7.10=pypi_0
timm=0.9.5=pypi_0
tk=8.6.12=h1ccaba5_0
tokenizers=0.14.1=pypi_0
tomli=2.0.1=pypi_0
toolz=0.12.0=pypi_0
torchaudio=0.12.1=py38_cu113
torchvision=0.13.1=py38_cu113
tornado=6.1=py38h0a891b7_3
tqdm=4.66.1=pypi_0
traitlets=5.9.0=pyhd8ed1ab_0
transformers=4.34.1=pypi_0
triton=2.0.0.dev20221120=pypi_0
types-python-dateutil=2.8.19.20240106=pypi_0
typing_extensions=4.7.1=py38h06a4308_0
uri-template=1.3.0=pypi_0
urllib3=1.26.16=py38h06a4308_0
wcwidth=0.2.6=pyhd8ed1ab_0
webcolors=1.13=pypi_0
websocket-client=1.7.0=pypi_0
werkzeug=2.3.7=pypi_0
wheel=0.38.4=py38h06a4308_0
xz=5.4.2=h5eee18b_0
yapf=0.40.1=pypi_0
zeromq=4.3.4=h9c3ff4c_1
zipp=3.16.2=pypi_0
zlib=1.2.13=h5eee18b_0
zstd=1.5.5=hc292b87_0
