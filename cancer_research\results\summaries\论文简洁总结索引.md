# 癌症论文简洁总结索引

## 📋 **快速导航**

### **主要文档**
- **[完整总结报告](./zotero_cancer_papers_summary_v1.md)** - 20篇癌症论文的详细分析

### **按研究领域分类**

#### **🎯 乳腺癌研究 (4篇)**
1. **致密乳腺癌检测** - EAGANet多视图融合架构
2. **多模态诊断综述** - 可解释AI方法回顾
3. **甲基化生物标志物** - XAI-MethylMarker框架
4. **多染色细胞检测** - Transformer域适应

#### **🫁 肺癌研究 (1篇)**
1. **影像病理组学整合** - 治疗预测和预后评估

#### **🔬 泛癌症研究 (4篇)**
1. **多模态生存预测** - 组织病理学-基因组学整合
2. **Cancer Cell经典论文** - 14种癌症类型分析
3. **桥接融合模型** - 可解释多模态框架
4. **早期检测框架** - AutoCancer自动化系统

#### **🎯 特定癌症类型 (4篇)**
1. **膀胱癌** - 新辅助化疗响应预测
2. **卵巢癌** - 多模态诊断模型
3. **胆囊癌** - 超声视频检测
4. **癌症基因研究** - 图异常分析方法

### **按技术方法分类**

#### **🤖 多模态AI技术**
- **影像+病理融合** (3篇)
- **基因组+组织学整合** (4篇)
- **临床+分子数据结合** (2篇)

#### **🔍 可解释AI方法**
- **SHAP值分析** (6篇)
- **Grad-CAM可视化** (5篇)
- **LIME局部解释** (3篇)
- **注意力机制** (8篇)

#### **🧠 深度学习架构**
- **CNN卷积网络** (12篇)
- **Transformer** (6篇)
- **图神经网络** (5篇)
- **自编码器** (4篇)

### **按期刊质量分类**

#### **🏆 顶级期刊 (1区)**
- **Cancer Cell** - 泛癌症多模态分析
- **Information Fusion** - 致密乳腺癌检测

#### **📊 高质量期刊 (2区)**
- **npj Precision Oncology** - 肺癌预测模型
- **Expert Systems with Applications** - 甲基化标志物

#### **📚 良好期刊 (3区)**
- **Frontiers in Medicine** - 乳腺癌诊断综述

## 🔥 **重点推荐论文**

### **1. 必读经典**
**Cancer Cell (2022)** - Pan-cancer integrative histology-genomic analysis
- 14种癌症类型的里程碑式研究
- 多模态深度学习的经典应用
- 开放数据库资源丰富

### **2. 技术创新**
**Information Fusion (2026)** - 致密乳腺癌检测
- LWM-Mamba创新架构
- 对抗训练增强鲁棒性
- 临床可解释性突出

### **3. 方法综述**
**Frontiers in Medicine (2024)** - 可解释多模态方法综述
- XAI技术全面回顾
- 多模态融合方法总结
- 未来发展方向指引

## 📈 **学习路径建议**

### **初学者路径**
1. 阅读综述论文了解领域概况
2. 学习基础的多模态融合概念
3. 理解可解释AI的基本方法
4. 实践简单的癌症检测任务

### **进阶研究者路径**
1. 深入研究Cancer Cell经典论文
2. 掌握高级多模态融合技术
3. 探索图神经网络在癌症中的应用
4. 开发新的可解释性方法

### **临床应用者路径**
1. 关注临床可解释性研究
2. 了解FDA批准的AI诊断工具
3. 学习如何评估AI模型的临床价值
4. 参与多中心临床验证研究

## 🛠️ **相关工具和资源**

### **开源代码库**
- **SHAP** - 模型解释工具
- **LIME** - 局部可解释性
- **PyTorch** - 深度学习框架
- **scikit-learn** - 机器学习库

### **数据集资源**
- **TCGA** - 癌症基因组图谱
- **MIMIC** - 医疗数据库
- **PathAI** - 病理图像数据

### **学习资源**
- **Coursera** - 医学AI课程
- **Papers with Code** - 论文代码实现
- **GitHub** - 开源项目

---

*最后更新：2025年7月31日*  
*论文数量：20篇*  
*覆盖时间：2022-2026年*
