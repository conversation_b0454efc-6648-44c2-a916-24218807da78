<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DeepRED规则提取过程演示</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        .title {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 40px;
            font-size: 2.5em;
            font-weight: bold;
        }
        
        .stage {
            margin-bottom: 50px;
            padding: 25px;
            border-radius: 10px;
            border-left: 5px solid #3498db;
            background: #f8f9fa;
        }
        
        .stage-title {
            font-size: 1.8em;
            color: #2980b9;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
        }
        
        .stage-icon {
            width: 40px;
            height: 40px;
            margin-right: 15px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5em;
        }
        
        .stage1 { border-left-color: #e74c3c; }
        .stage1 .stage-icon { background: #e74c3c; color: white; }
        
        .stage2 { border-left-color: #f39c12; }
        .stage2 .stage-icon { background: #f39c12; color: white; }
        
        .stage3 { border-left-color: #27ae60; }
        .stage3 .stage-icon { background: #27ae60; color: white; }
        
        .neural-network {
            display: flex;
            justify-content: space-around;
            align-items: center;
            margin: 30px 0;
            padding: 20px;
            background: #ecf0f1;
            border-radius: 10px;
        }
        
        .layer {
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        
        .layer-title {
            font-weight: bold;
            margin-bottom: 10px;
            color: #34495e;
        }
        
        .neuron {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            margin: 3px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .neuron.active {
            background: #e74c3c;
            color: white;
            box-shadow: 0 0 15px rgba(231, 76, 60, 0.6);
            transform: scale(1.1);
        }
        
        .neuron.inactive {
            background: #bdc3c7;
            color: #7f8c8d;
        }
        
        .pattern-display {
            background: #2c3e50;
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            font-family: 'Courier New', monospace;
        }
        
        .rule-box {
            background: #27ae60;
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            font-size: 1.2em;
            text-align: center;
        }
        
        .control-panel {
            text-align: center;
            margin: 30px 0;
        }
        
        .btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            font-size: 1.1em;
            cursor: pointer;
            margin: 0 10px;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            background: #2980b9;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
        }
        
        .sample-info {
            text-align: center;
            font-size: 1.3em;
            margin: 20px 0;
            padding: 15px;
            background: #f1c40f;
            border-radius: 10px;
            color: #2c3e50;
        }
        
        .explanation {
            background: #e8f5e8;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border-left: 4px solid #27ae60;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
        
        .highlight {
            animation: pulse 1s infinite;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">🧠 DeepRED规则提取过程演示</h1>
        
        <div class="stage stage1">
            <div class="stage-title">
                <div class="stage-icon">1</div>
                阶段1：激活模式捕获
            </div>
            <p><strong>目标：</strong>记录神经网络在处理不同样本时的"思维过程"</p>
            
            <div class="sample-info" id="sampleInfo">
                当前样本：肺炎患者的X光片
            </div>
            
            <div class="neural-network">
                <div class="layer">
                    <div class="layer-title">输入层</div>
                    <div class="neuron active">📷</div>
                </div>
                
                <div class="layer">
                    <div class="layer-title">第3层<br>(边缘检测)</div>
                    <div class="neuron" id="n3_1">1</div>
                    <div class="neuron" id="n3_2">2</div>
                    <div class="neuron" id="n3_3">3</div>
                    <div class="neuron" id="n3_4">4</div>
                </div>
                
                <div class="layer">
                    <div class="layer-title">第4层<br>(特征识别)</div>
                    <div class="neuron" id="n4_1">阴</div>
                    <div class="neuron" id="n4_2">炎</div>
                    <div class="neuron" id="n4_3">纹</div>
                </div>
                
                <div class="layer">
                    <div class="layer-title">第5层<br>(综合判断)</div>
                    <div class="neuron" id="n5_1">健</div>
                    <div class="neuron" id="n5_2">病</div>
                </div>
                
                <div class="layer">
                    <div class="layer-title">输出层</div>
                    <div class="neuron" id="output">?</div>
                </div>
            </div>
            
            <div class="pattern-display" id="patternDisplay">
                激活模式记录：等待开始演示...
            </div>
        </div>
        
        <div class="stage stage2">
            <div class="stage-title">
                <div class="stage-icon">2</div>
                阶段2：模式分析
            </div>
            <p><strong>目标：</strong>分析大量样本，找出肺炎患者和正常人的激活模式差异</p>
            
            <div class="explanation" id="patternAnalysis">
                <h4>🔍 模式分析结果：</h4>
                <p>点击"开始演示"按钮查看分析过程...</p>
            </div>
        </div>
        
        <div class="stage stage3">
            <div class="stage-title">
                <div class="stage-icon">3</div>
                阶段3：规则生成
            </div>
            <p><strong>目标：</strong>将发现的模式转换为可理解的诊断规则</p>
            
            <div class="rule-box" id="generatedRule">
                📋 生成的规则将在这里显示...
            </div>
        </div>
        
        <div class="control-panel">
            <button class="btn" onclick="startDemo()">🚀 开始演示</button>
            <button class="btn" onclick="showPneumoniaCase()">🫁 肺炎案例</button>
            <button class="btn" onclick="showNormalCase()">✅ 正常案例</button>
            <button class="btn" onclick="generateRules()">⚡ 生成规则</button>
        </div>
    </div>

    <script>
        let currentSample = 'pneumonia';
        let demoStep = 0;
        
        // 预定义的激活模式
        const patterns = {
            pneumonia: {
                layer3: [1, 0, 1, 1],
                layer4: [1, 1, 0],  // 阴影=1, 炎症=1, 纹理=0
                layer5: [0, 1],     // 健康=0, 病变=1
                output: 1
            },
            normal: {
                layer3: [0, 1, 0, 1],
                layer4: [0, 0, 1],  // 阴影=0, 炎症=0, 纹理=1
                layer5: [1, 0],     // 健康=1, 病变=0
                output: 0
            }
        };
        
        function updateNeuronStates(pattern) {
            // 更新第3层
            for (let i = 0; i < 4; i++) {
                const neuron = document.getElementById(`n3_${i+1}`);
                neuron.className = pattern.layer3[i] ? 'neuron active' : 'neuron inactive';
            }
            
            // 更新第4层
            const layer4Names = ['阴', '炎', '纹'];
            for (let i = 0; i < 3; i++) {
                const neuron = document.getElementById(`n4_${i+1}`);
                neuron.className = pattern.layer4[i] ? 'neuron active' : 'neuron inactive';
            }
            
            // 更新第5层
            const layer5Names = ['健', '病'];
            for (let i = 0; i < 2; i++) {
                const neuron = document.getElementById(`n5_${i+1}`);
                neuron.className = pattern.layer5[i] ? 'neuron active' : 'neuron inactive';
            }
            
            // 更新输出
            const output = document.getElementById('output');
            output.textContent = pattern.output ? '🫁' : '✅';
            output.className = 'neuron active';
        }
        
        function updatePatternDisplay(sampleType) {
            const pattern = patterns[sampleType];
            const display = document.getElementById('patternDisplay');
            
            display.innerHTML = `
                <strong>样本类型：${sampleType === 'pneumonia' ? '肺炎患者' : '正常人'}</strong><br>
                第3层激活：[${pattern.layer3.join(', ')}] (边缘检测模式)<br>
                第4层激活：[${pattern.layer4.join(', ')}] (阴影检测, 炎症检测, 纹理检测)<br>
                第5层激活：[${pattern.layer5.join(', ')}] (健康标志, 病变标志)<br>
                最终输出：${pattern.output ? '肺炎' : '正常'}
            `;
        }
        
        function startDemo() {
            showPneumoniaCase();
            setTimeout(() => {
                showNormalCase();
                setTimeout(() => {
                    analyzePatterns();
                }, 2000);
            }, 3000);
        }
        
        function showPneumoniaCase() {
            currentSample = 'pneumonia';
            document.getElementById('sampleInfo').textContent = '当前样本：肺炎患者的X光片';
            updateNeuronStates(patterns.pneumonia);
            updatePatternDisplay('pneumonia');
        }
        
        function showNormalCase() {
            currentSample = 'normal';
            document.getElementById('sampleInfo').textContent = '当前样本：正常人的X光片';
            updateNeuronStates(patterns.normal);
            updatePatternDisplay('normal');
        }
        
        function analyzePatterns() {
            const analysis = document.getElementById('patternAnalysis');
            analysis.innerHTML = `
                <h4>🔍 模式分析结果：</h4>
                <p><strong>肺炎患者的共同特征：</strong></p>
                <ul>
                    <li>90% 的患者：阴影检测器被激活 (第4层神经元1)</li>
                    <li>85% 的患者：炎症检测器被激活 (第4层神经元2)</li>
                    <li>95% 的患者：病变标志被激活 (第5层神经元2)</li>
                </ul>
                <p><strong>正常人的共同特征：</strong></p>
                <ul>
                    <li>95% 的正常人：阴影检测器未激活</li>
                    <li>90% 的正常人：健康标志被激活 (第5层神经元1)</li>
                    <li>85% 的正常人：纹理检测器被激活 (第4层神经元3)</li>
                </ul>
            `;
        }
        
        function generateRules() {
            const ruleBox = document.getElementById('generatedRule');
            ruleBox.innerHTML = `
                <h3>🎯 提取的诊断规则</h3>
                <div style="text-align: left; margin: 20px 0;">
                    <p><strong>规则1：</strong> 如果 (阴影检测器=激活 且 炎症检测器=激活) 则 诊断=肺炎</p>
                    <p style="margin-left: 20px;">置信度：89% | 支持度：45个样本</p>
                    
                    <p><strong>规则2：</strong> 如果 (健康标志=激活 且 阴影检测器=未激活) 则 诊断=正常</p>
                    <p style="margin-left: 20px;">置信度：95% | 支持度：78个样本</p>
                    
                    <p><strong>规则3：</strong> 如果 (病变标志=激活) 则 诊断=肺炎</p>
                    <p style="margin-left: 20px;">置信度：92% | 支持度：52个样本</p>
                </div>
            `;
            
            analyzePatterns();
        }
        
        // 初始化显示
        showPneumoniaCase();
    </script>
</body>
</html>
