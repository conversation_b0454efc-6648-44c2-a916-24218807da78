<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ali等人2023年XAI综述 - 完整图表集</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        .paper-header {
            text-align: center;
            margin-bottom: 50px;
            padding-bottom: 30px;
            border-bottom: 3px solid #3498db;
        }
        
        .paper-header h1 {
            color: #2c3e50;
            font-size: 24px;
            margin-bottom: 10px;
        }
        
        .paper-header .citation {
            color: #7f8c8d;
            font-size: 14px;
            font-style: italic;
        }
        
        .chart-section {
            margin-bottom: 60px;
            padding: 30px;
            background: #f8f9fa;
            border-radius: 15px;
            border-left: 5px solid #3498db;
        }
        
        .chart-title {
            color: #2c3e50;
            font-size: 20px;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .framework-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .dimension-card {
            padding: 20px;
            border-radius: 10px;
            color: white;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .data-explainability { background: linear-gradient(135deg, #3498db, #2980b9); }
        .model-explainability { background: linear-gradient(135deg, #e74c3c, #c0392b); }
        .posthoc-explainability { background: linear-gradient(135deg, #f39c12, #e67e22); }
        .explanation-evaluation { background: linear-gradient(135deg, #27ae60, #229954); }
        
        .dimension-card h4 {
            margin: 0 0 15px 0;
            font-size: 16px;
        }
        
        .dimension-card ul {
            margin: 0;
            padding-left: 20px;
            line-height: 1.6;
            font-size: 14px;
        }
        
        .center-connection {
            text-align: center;
            background: #34495e;
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px auto;
            max-width: 300px;
        }
        
        .taxonomy-tree {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 20px;
        }
        
        .tree-level {
            display: flex;
            justify-content: center;
            gap: 30px;
            flex-wrap: wrap;
        }
        
        .tree-node {
            background: white;
            border: 2px solid #3498db;
            border-radius: 10px;
            padding: 15px 20px;
            text-align: center;
            min-width: 150px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .tree-node.root {
            background: #3498db;
            color: white;
            font-weight: bold;
        }
        
        .tree-node.category {
            background: #ecf0f1;
            border-color: #95a5a6;
        }
        
        .tree-node.method {
            background: #e8f5e8;
            border-color: #27ae60;
            font-size: 14px;
        }
        
        .connection-line {
            width: 2px;
            height: 20px;
            background: #bdc3c7;
            margin: 0 auto;
        }
        
        .explanation-text {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin-top: 20px;
            line-height: 1.6;
            color: #555;
        }
        
        .highlight {
            background: #fff3cd;
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="paper-header">
            <h1>Explainable Artificial Intelligence (XAI): What we know and what is left to attain Trustworthy Artificial Intelligence</h1>
            <div class="citation">
                Ali, S., Abuhmed, T., El-Sappagh, S., Muhammad, K., et al. (2023)<br>
                Information Fusion, Volume 99, 中科院1区期刊
            </div>
        </div>
        
        <!-- 图表1：四维分类框架 -->
        <div class="chart-section">
            <h2 class="chart-title">📊 图表1：XAI四维分类框架</h2>
            
            <div class="framework-grid">
                <div class="dimension-card data-explainability">
                    <h4>📊 数据可解释性 (Data Explainability)</h4>
                    <ul>
                        <li>数据收集过程透明化</li>
                        <li>预处理步骤合理性</li>
                        <li>数据质量评估</li>
                        <li>偏差识别与处理</li>
                    </ul>
                </div>
                
                <div class="dimension-card model-explainability">
                    <h4>🔧 模型可解释性 (Model Explainability)</h4>
                    <ul>
                        <li>内在可解释模型</li>
                        <li>架构设计原理</li>
                        <li>参数意义解释</li>
                        <li>决策边界可视化</li>
                    </ul>
                </div>
                
                <div class="dimension-card posthoc-explainability">
                    <h4>🔍 后验可解释性 (Post-hoc Explainability)</h4>
                    <ul>
                        <li>特征重要性分析</li>
                        <li>局部解释方法</li>
                        <li>全局解释技术</li>
                        <li>反事实解释</li>
                    </ul>
                </div>
                
                <div class="dimension-card explanation-evaluation">
                    <h4>✅ 解释评估 (Assessment of Explanations)</h4>
                    <ul>
                        <li>忠实度测量</li>
                        <li>可理解性评估</li>
                        <li>稳定性检验</li>
                        <li>用户研究</li>
                    </ul>
                </div>
            </div>
            
            <div class="center-connection">
                <strong>🎯 可信AI (Trustworthy AI)</strong><br>
                <small>四维协同实现全面可解释性</small>
            </div>
            
            <div class="explanation-text">
                <strong>框架解读：</strong>这个四维框架是论文的核心贡献，将XAI从传统的二维分类（如局部vs全局）扩展为覆盖AI系统全生命周期的四个维度。
                <span class="highlight">数据维度</span>确保输入透明，
                <span class="highlight">模型维度</span>确保算法透明，
                <span class="highlight">后验维度</span>确保输出可解释，
                <span class="highlight">评估维度</span>确保解释质量。
            </div>
        </div>
        
        <!-- 图表2：XAI技术演进时间线 -->
        <div class="chart-section">
            <h2 class="chart-title">🚀 图表2：XAI技术演进时间线</h2>

            <div style="position: relative; padding: 40px 0;">
                <!-- 时间轴主线 -->
                <div style="position: absolute; left: 50%; top: 0; bottom: 0; width: 4px; background: linear-gradient(to bottom, #3498db, #e74c3c, #f39c12, #27ae60, #9b59b6, #34495e); transform: translateX(-50%);"></div>

                <!-- 2016年 - CAM -->
                <div style="display: flex; align-items: center; margin-bottom: 50px;">
                    <div style="flex: 1; text-align: right; padding-right: 30px;">
                        <div style="background: linear-gradient(135deg, #3498db, #2980b9); color: white; padding: 20px; border-radius: 10px; box-shadow: 0 4px 8px rgba(0,0,0,0.1);">
                            <h4 style="margin: 0 0 10px 0; font-size: 16px;">2016年 - CAM开创性突破</h4>
                            <p style="margin: 0; font-size: 14px; line-height: 1.5;">Zhou等人提出类激活映射，首次实现CNN视觉解释，开启深度学习可视化新纪元</p>
                        </div>
                    </div>
                    <div style="width: 20px; height: 20px; background: #3498db; border-radius: 50%; border: 4px solid white; box-shadow: 0 0 0 4px #3498db; z-index: 10;"></div>
                    <div style="flex: 1; padding-left: 30px;"></div>
                </div>

                <!-- 2017年 - Grad-CAM & Integrated Gradients -->
                <div style="display: flex; align-items: center; margin-bottom: 50px;">
                    <div style="flex: 1; padding-right: 30px;"></div>
                    <div style="width: 20px; height: 20px; background: #e74c3c; border-radius: 50%; border: 4px solid white; box-shadow: 0 0 0 4px #e74c3c; z-index: 10;"></div>
                    <div style="flex: 1; text-align: left; padding-left: 30px;">
                        <div style="background: linear-gradient(135deg, #e74c3c, #c0392b); color: white; padding: 20px; border-radius: 10px; box-shadow: 0 4px 8px rgba(0,0,0,0.1);">
                            <h4 style="margin: 0 0 10px 0; font-size: 16px;">2017年 - 双重技术突破</h4>
                            <p style="margin: 0; font-size: 14px; line-height: 1.5;">Grad-CAM解决架构限制，积分梯度建立理论基础，满足重要数学公理</p>
                        </div>
                    </div>
                </div>

                <!-- 2018年 - SHAP -->
                <div style="display: flex; align-items: center; margin-bottom: 50px;">
                    <div style="flex: 1; text-align: right; padding-right: 30px;">
                        <div style="background: linear-gradient(135deg, #f39c12, #e67e22); color: white; padding: 20px; border-radius: 10px; box-shadow: 0 4px 8px rgba(0,0,0,0.1);">
                            <h4 style="margin: 0 0 10px 0; font-size: 16px;">2018年 - SHAP统一框架</h4>
                            <p style="margin: 0; font-size: 14px; line-height: 1.5;">Lundberg基于Shapley值统一解释方法，成为特征重要性分析的黄金标准</p>
                        </div>
                    </div>
                    <div style="width: 20px; height: 20px; background: #f39c12; border-radius: 50%; border: 4px solid white; box-shadow: 0 0 0 4px #f39c12; z-index: 10;"></div>
                    <div style="flex: 1; padding-left: 30px;"></div>
                </div>

                <!-- 2019年 - Grad-CAM++ -->
                <div style="display: flex; align-items: center; margin-bottom: 50px;">
                    <div style="flex: 1; padding-right: 30px;"></div>
                    <div style="width: 20px; height: 20px; background: #9b59b6; border-radius: 50%; border: 4px solid white; box-shadow: 0 0 0 4px #9b59b6; z-index: 10;"></div>
                    <div style="flex: 1; text-align: left; padding-left: 30px;">
                        <div style="background: linear-gradient(135deg, #9b59b6, #8e44ad); color: white; padding: 20px; border-radius: 10px; box-shadow: 0 4px 8px rgba(0,0,0,0.1);">
                            <h4 style="margin: 0 0 10px 0; font-size: 16px;">2019年 - Grad-CAM++精细化</h4>
                            <p style="margin: 0; font-size: 14px; line-height: 1.5;">引入像素级权重，显著改进多对象和小对象的定位精度</p>
                        </div>
                    </div>
                </div>

                <!-- 2020年 - Score-CAM -->
                <div style="display: flex; align-items: center; margin-bottom: 50px;">
                    <div style="flex: 1; text-align: right; padding-right: 30px;">
                        <div style="background: linear-gradient(135deg, #27ae60, #229954); color: white; padding: 20px; border-radius: 10px; box-shadow: 0 4px 8px rgba(0,0,0,0.1);">
                            <h4 style="margin: 0 0 10px 0; font-size: 16px;">2020年 - Score-CAM无梯度创新</h4>
                            <p style="margin: 0; font-size: 14px; line-height: 1.5;">完全摒弃梯度信息，解决梯度饱和问题，提供更稳定的解释</p>
                        </div>
                    </div>
                    <div style="width: 20px; height: 20px; background: #27ae60; border-radius: 50%; border: 4px solid white; box-shadow: 0 0 0 4px #27ae60; z-index: 10;"></div>
                    <div style="flex: 1; padding-left: 30px;"></div>
                </div>

                <!-- 2023年 - 当前综述 -->
                <div style="display: flex; align-items: center;">
                    <div style="flex: 1; padding-right: 30px;"></div>
                    <div style="width: 20px; height: 20px; background: #34495e; border-radius: 50%; border: 4px solid white; box-shadow: 0 0 0 4px #34495e; z-index: 10;"></div>
                    <div style="flex: 1; text-align: left; padding-left: 30px;">
                        <div style="background: linear-gradient(135deg, #34495e, #2c3e50); color: white; padding: 20px; border-radius: 10px; box-shadow: 0 4px 8px rgba(0,0,0,0.1);">
                            <h4 style="margin: 0 0 10px 0; font-size: 16px;">2023年 - 系统性综述</h4>
                            <p style="margin: 0; font-size: 14px; line-height: 1.5;">Ali等人四维框架，构建完整XAI生态，标志着从技术向系统的转变</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="explanation-text">
                <strong>演进解读：</strong>XAI技术发展体现了从经验驱动向理论指导的转变。
                <span class="highlight">CAM→Grad-CAM</span>解决架构限制，
                <span class="highlight">Grad-CAM→Grad-CAM++</span>改进定位精度，
                <span class="highlight">Score-CAM</span>解决梯度依赖，
                <span class="highlight">SHAP</span>建立理论基础，
                最终形成Ali等人的<span class="highlight">系统性四维框架</span>。
            </div>
        </div>

        <!-- 图表3：XAI工具对比表格 -->
        <div class="chart-section">
            <h2 class="chart-title">🛠️ 图表3：主流XAI工具对比分析</h2>

            <table style="width: 100%; border-collapse: collapse; margin-bottom: 20px; box-shadow: 0 4px 8px rgba(0,0,0,0.1); border-radius: 10px; overflow: hidden;">
                <thead>
                    <tr style="background: linear-gradient(135deg, #34495e, #2c3e50); color: white;">
                        <th style="padding: 15px; text-align: left; font-size: 14px;">工具名称</th>
                        <th style="padding: 15px; text-align: left; font-size: 14px;">核心优势</th>
                        <th style="padding: 15px; text-align: left; font-size: 14px;">主要局限</th>
                        <th style="padding: 15px; text-align: left; font-size: 14px;">医学AI应用</th>
                    </tr>
                </thead>
                <tbody>
                    <tr style="background: #f8f9fa;">
                        <td style="padding: 12px; font-weight: bold; color: #2c3e50;">SHAP</td>
                        <td style="padding: 12px; font-size: 13px; color: #27ae60;">理论基础扎实(Shapley值)<br>满足四个重要公理<br>支持多种模型类型</td>
                        <td style="padding: 12px; font-size: 13px; color: #e74c3c;">计算复杂度高(2ⁿ)<br>大数据集处理慢<br>需要大量模型查询</td>
                        <td style="padding: 12px; font-size: 13px;">分析临床指标贡献度<br>量化检查项目重要性<br>辅助诊断决策解释</td>
                    </tr>
                    <tr style="background: white;">
                        <td style="padding: 12px; font-weight: bold; color: #2c3e50;">LIME</td>
                        <td style="padding: 12px; font-size: 13px; color: #27ae60;">局部解释直观易懂<br>模型无关性强<br>实现相对简单</td>
                        <td style="padding: 12px; font-size: 13px; color: #e74c3c;">解释稳定性差<br>对超参数敏感<br>局部性限制</td>
                        <td style="padding: 12px; font-size: 13px;">解释单个病例诊断<br>医学影像局部分析<br>个性化治疗建议</td>
                    </tr>
                    <tr style="background: #f8f9fa;">
                        <td style="padding: 12px; font-weight: bold; color: #2c3e50;">Grad-CAM</td>
                        <td style="padding: 12px; font-size: 13px; color: #27ae60;">视觉解释直观<br>无架构限制<br>计算效率高</td>
                        <td style="padding: 12px; font-size: 13px; color: #e74c3c;">仅适用于CNN<br>分辨率受限<br>多对象定位困难</td>
                        <td style="padding: 12px; font-size: 13px;">医学影像病变定位<br>放射学AI解释<br>病理图像分析</td>
                    </tr>
                    <tr style="background: white;">
                        <td style="padding: 12px; font-weight: bold; color: #2c3e50;">积分梯度</td>
                        <td style="padding: 12px; font-size: 13px; color: #27ae60;">理论基础严格<br>满足重要公理<br>归因精度高</td>
                        <td style="padding: 12px; font-size: 13px; color: #e74c3c;">基线选择主观<br>计算成本较高<br>需要积分近似</td>
                        <td style="padding: 12px; font-size: 13px;">基因序列分析<br>药物分子设计<br>医学文本挖掘</td>
                    </tr>
                </tbody>
            </table>

            <div class="explanation-text">
                <strong>工具选择指南：</strong>
                <span class="highlight">SHAP</span>适合需要理论严谨性的场景，
                <span class="highlight">LIME</span>适合快速原型和局部解释，
                <span class="highlight">Grad-CAM</span>适合医学影像可视化，
                <span class="highlight">积分梯度</span>适合需要精确归因的研究。
                在医学AI中，通常需要组合使用多种工具以获得全面的解释。
            </div>
        </div>
    </div>
</body>
</html>
