#!/usr/bin/env python3
"""
FocusMAE快速设置和测试脚本
用于快速验证项目环境和基本功能

作者：基于FocusMAE项目改编
日期：2025-07-31
版本：v1.0

主要功能：
1. 检查环境依赖
2. 验证模型加载
3. 测试基本推理功能
4. 生成示例数据进行测试
"""

import os
import sys
import torch
import numpy as np
import warnings
warnings.filterwarnings('ignore')

def check_environment():
    """检查运行环境"""
    print("🔍 检查运行环境...")
    
    # 检查Python版本
    python_version = sys.version_info
    print(f"Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    # 检查PyTorch
    print(f"PyTorch版本: {torch.__version__}")
    print(f"CUDA可用: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        print(f"CUDA版本: {torch.version.cuda}")
        print(f"GPU数量: {torch.cuda.device_count()}")
        for i in range(torch.cuda.device_count()):
            print(f"GPU {i}: {torch.cuda.get_device_name(i)}")
    
    # 检查关键依赖
    try:
        import timm
        print(f"timm版本: {timm.__version__}")
    except ImportError:
        print("❌ timm未安装")
        return False
    
    try:
        import einops
        print(f"einops版本: {einops.__version__}")
    except ImportError:
        print("❌ einops未安装")
        return False
    
    try:
        import cv2
        print(f"OpenCV版本: {cv2.__version__}")
    except ImportError:
        print("❌ OpenCV未安装")
        return False
    
    print("✅ 环境检查完成")
    return True

def test_model_loading():
    """测试模型加载"""
    print("\n🧠 测试模型加载...")
    
    try:
        # 切换到FocusMAE_v1目录
        original_path = os.getcwd()
        focusmae_path = os.path.join(os.path.dirname(__file__), 'FocusMAE_v1')
        
        if not os.path.exists(focusmae_path):
            print("❌ FocusMAE_v1目录不存在")
            return False
        
        os.chdir(focusmae_path)
        sys.path.insert(0, focusmae_path)
        
        # 导入模型
        from models.vit_timesformer import VisionTransformer
        
        # 创建模型实例
        model = VisionTransformer(
            img_size=224,
            patch_size=16,
            embed_dim=768,
            depth=12,
            num_heads=12,
            num_classes=2,  # 二分类：良性/恶性
            num_frames=8,   # 视频帧数
            attention_type='divided_space_time'
        )
        
        print(f"✅ 模型创建成功")
        print(f"模型参数数量: {sum(p.numel() for p in model.parameters()):,}")
        
        # 恢复原始路径
        os.chdir(original_path)
        sys.path.remove(focusmae_path)
        
        return True, model
        
    except Exception as e:
        print(f"❌ 模型加载失败: {str(e)}")
        os.chdir(original_path)
        if focusmae_path in sys.path:
            sys.path.remove(focusmae_path)
        return False, None

def test_inference():
    """测试推理功能"""
    print("\n🔮 测试推理功能...")
    
    success, model = test_model_loading()
    if not success:
        return False
    
    try:
        # 创建模拟输入数据
        batch_size = 2
        num_frames = 8
        channels = 3
        height = 224
        width = 224
        
        # 模拟视频数据 (B, T, C, H, W)
        dummy_video = torch.randn(batch_size, num_frames, channels, height, width)
        print(f"输入数据形状: {dummy_video.shape}")
        
        # 设置模型为评估模式
        model.eval()
        
        # 前向推理
        with torch.no_grad():
            outputs = model(dummy_video)
        
        print(f"输出形状: {outputs.shape}")
        print(f"输出范围: [{outputs.min().item():.4f}, {outputs.max().item():.4f}]")
        
        # 计算预测概率
        probabilities = torch.softmax(outputs, dim=1)
        predictions = torch.argmax(probabilities, dim=1)
        
        print(f"预测概率: {probabilities}")
        print(f"预测类别: {predictions}")
        
        print("✅ 推理测试成功")
        return True
        
    except Exception as e:
        print(f"❌ 推理测试失败: {str(e)}")
        return False

def generate_sample_data():
    """生成示例数据用于测试"""
    print("\n📊 生成示例数据...")
    
    try:
        # 创建示例数据目录
        data_dir = "sample_data"
        os.makedirs(data_dir, exist_ok=True)
        
        # 生成模拟超声视频数据
        num_videos = 4
        num_frames = 16
        height, width = 224, 224
        
        for i in range(num_videos):
            # 生成随机视频数据
            video_data = np.random.randint(0, 255, (num_frames, height, width, 3), dtype=np.uint8)
            
            # 添加一些模拟的超声特征
            # 中心区域亮度较高
            center_y, center_x = height // 2, width // 2
            radius = min(height, width) // 4
            
            for frame in range(num_frames):
                y, x = np.ogrid[:height, :width]
                mask = (x - center_x) ** 2 + (y - center_y) ** 2 <= radius ** 2
                video_data[frame][mask] = np.clip(video_data[frame][mask] + 50, 0, 255)
            
            # 保存视频数据
            video_path = os.path.join(data_dir, f"sample_video_{i}.npy")
            np.save(video_path, video_data)
            
            # 生成标签（随机分配良性/恶性）
            label = np.random.randint(0, 2)
            label_path = os.path.join(data_dir, f"sample_video_{i}_label.txt")
            with open(label_path, 'w') as f:
                f.write(str(label))
        
        print(f"✅ 生成了{num_videos}个示例视频")
        print(f"数据保存在: {os.path.abspath(data_dir)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据生成失败: {str(e)}")
        return False

def create_quick_test_script():
    """创建快速测试脚本"""
    print("\n📝 创建快速测试脚本...")
    
    test_script = """#!/bin/bash
# FocusMAE快速测试脚本

echo "🚀 开始FocusMAE快速测试..."

# 检查是否在正确目录
if [ ! -d "FocusMAE_v1" ]; then
    echo "❌ 请在FocusMAE-main目录下运行此脚本"
    exit 1
fi

# 进入FocusMAE_v1目录
cd FocusMAE_v1

# 检查Python环境
echo "🐍 检查Python环境..."
python --version
python -c "import torch; print(f'PyTorch: {torch.__version__}')"
python -c "import timm; print(f'timm: {timm.__version__}')"

# 运行基本模型测试
echo "🧠 测试模型加载..."
python -c "
import sys
sys.path.append('.')
from models.vit_timesformer import VisionTransformer
import torch

model = VisionTransformer(
    img_size=224, patch_size=16, embed_dim=768,
    depth=12, num_heads=12, num_classes=2,
    num_frames=8, attention_type='divided_space_time'
)

dummy_input = torch.randn(1, 8, 3, 224, 224)
with torch.no_grad():
    output = model(dummy_input)
print(f'✅ 模型测试成功，输出形状: {output.shape}')
"

echo "✅ 快速测试完成！"
"""
    
    with open("quick_test.sh", "w") as f:
        f.write(test_script)
    
    # 设置执行权限（Linux/Mac）
    try:
        os.chmod("quick_test.sh", 0o755)
    except:
        pass
    
    print("✅ 创建了quick_test.sh脚本")

def main():
    """主函数"""
    print("🎯 FocusMAE快速设置和测试")
    print("=" * 50)
    
    # 检查环境
    if not check_environment():
        print("❌ 环境检查失败，请安装必要依赖")
        return
    
    # 测试模型加载
    if not test_model_loading()[0]:
        print("❌ 模型加载失败")
        return
    
    # 测试推理
    if not test_inference():
        print("❌ 推理测试失败")
        return
    
    # 生成示例数据
    generate_sample_data()
    
    # 创建快速测试脚本
    create_quick_test_script()
    
    print("\n🎉 所有测试完成！")
    print("\n📋 下一步建议：")
    print("1. 下载预训练权重：https://drive.google.com/drive/folders/16E1EDl323GFAbmQ02fqVQwVkkz-4GBZY")
    print("2. 准备胆囊超声视频数据")
    print("3. 运行预训练：bash pretrain.sh")
    print("4. 运行微调：bash finetune.sh")
    print("5. 使用quick_test.sh进行快速验证")

if __name__ == "__main__":
    main()
